# 概要

下記３つのプロジェクトが入っています。

- エージェントサーバー
  - server
- バッチ
  - cli
- E2Eクライアント
  - e2e

# 開発者向け

## プロジェクト共通

### 開発言語とバージョン

Python 3.12

#### 備考

バッチに利用されている[typer](https://github.com/fastapi/typer)は3.13でうまく動作しないらしいです。

そのため、3.12を使っています。

#### Pythonライブラリ

pyproject.tomlに直接利用ライブラリを記入していますので、そのままインストールすれば良いですが、

直接利用ライブラリが依頼するライブラリのマイナーバージョンアップよりアプリが動かなくなる場合があります。

そのため、動くバージョンの`requirement.txtを生成して格納しています。

### 設定ファイル
- pyproject.toml
  - Pythonプロジェクト設定
  - 依頼ライブラリ
    - 本来は直接利用ライブラリだけ記入して、必要なライブラリは全部自動的にインストールされますが、直接利用ライブラリが依頼するライブラリのマイナーバージョンアップより、アプリが動かなくなる場合があります。
    - そのため動く時に必要なライブラリとバージョンを全部正確に記入しています。
- config.yml
  - Logger、DBなどの設定
- .env.local
  - 環境変数

### コンテナを使わず、ローカルデバッグのための準備

#### Pythonインストール

`brew install python@3.12`
よりインストールできます。

#### ライブラリインストール

Terminalを起動して下記実行します。
```
deactivate
source .venv/bin/activate
cd server
pip install .
cd ../cli
pip install .
cd ../e2e
pip install .
```

#### VSCode Pluginインストール

- `Python`
- `Python Debugger`

#### `venv`作成

[Python environments in VS Code](https://code.visualstudio.com/docs/python/environments)参照していただきたいのですが、ステップは：

1. VSCodeで`Shift+Command+P`でコマンド入力欄
2. `Python: Create Environment`を検索
3. Environment typeでは`Venv`を選択
4. Interpreterでは`Python 3.12`を選択

##### 備考

- `venv`はローカルですでにインストールされたPythonを利用しますので、先にPythonをインストールしてください。
- 実行時に作られた`venv`を利用するため
  - VSCodeでpythonの`.py`ファイルを開きます（どのファイルでもOK）
  - VSCodeの右下のpyhonのバージョンが表示されます。**重要**：ここに`3.12(.venv)`と書いていなければグローバルのpythonを使っているので、必ず`(.venv)`が記載されていることを確認してください。もし`3.12`だけで`(.venv)`の記載がなければ、そのバージョン番号を押して`3.12(.venv)`を選択してください

#### VSCodeのデバッグ実行方法

1. VSCodeの左の三角ボタンを押すとデバッグモードに切り替わります
2. VSCodeの左上のドロップダウンに実行したいのを選択してください
3. ドロップダウンの左の三角ボタンを押して起動します

## プロジェクト毎

それぞれの`README.md`を参照してください。
