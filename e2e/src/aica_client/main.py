import asyncio
import datetime
import os
import re
from typing import Any
import yaml
from pathlib import Path

from client.e2e_client import E2EClient
from repositories.llm_repo import LLMRepository

stats = {"conversations": []}


def load_config():
    """
    config.ymlから設定を読み込んで、環境変数の差し替えを行います。

    Returns:
        dict: 設定
    """
    with open("config.yml", "r") as f:
        content = f.read()
    # 環境変数の差し替え
    content = re.sub(r"\$\{(\w+)\}", lambda m: os.getenv(m.group(1), ""), content)
    return yaml.safe_load(content)


def load_personas(persona_location, persona_included=None, persona_excluded=None):
    """
    ペルソナ読み込み

    Args:
        persona_location (str): ペルソナの保存先
        persona_included (list, optional): 利用対象
        persona_excluded (list, optional): 除外対象。persona_includedが定義された場合、無視

    Returns:
        list: ペルソナのリスト。利用対象のmax_turnsが定義された場合、それも入っている。
    """
    personas = []
    persona_dir = Path(persona_location)
    excluded_names = {p["name"] for p in persona_excluded or []}

    if persona_included:
        for persona_config in persona_included:
            name = persona_config["name"]
            file_path = persona_dir / f"{name}.md"
            if file_path.exists():
                with open(file_path, "r") as f:
                    content = f.read().strip()
                    max_turns = persona_config.get("max_turns")
                    personas.append((content, max_turns, name))
            else:
                print(f"ペルソナが存在しない: {file_path}")
                return []
    else:
        for file_path in persona_dir.glob("*.md"):
            name = file_path.stem
            if name not in excluded_names:
                with open(file_path, "r") as f:
                    content = f.read().strip()
                    personas.append((content, None, name))

    return personas


def create_client_configs(config):
    """
    モデルとペルソナを組み合わせて、クライアントの設定を作成します。

    Args:
        config (dict): 設定

    Returns:
        list: (
            model: モデル設定
            persona_content: 該当ペルソナ用のシステムプロンプト
            max_turns: 最大会話数
            persona_name: ペルソナ名
            model_name: モデル名
        )
    """
    persona_included = config.get("persona_included")
    persona_excluded = config.get("persona_excluded")
    personas = load_personas(
        config["persona_location"], persona_included, persona_excluded
    )
    if not personas:
        print("ペルソナ読み込みが失敗しました。")
        return None

    models = [m for m in config["model_list"] if not m.get("disabled", False)]
    if not models:
        print("モデルを定義してください。")
        return None

    client_number = config["client_number"]
    global_max_turns = config.get("max_turns", 0)

    if client_number == 0:
        client_number = len(personas)

    configs = []
    for i in range(client_number):
        persona_content, persona_max_turns, persona_name = personas[i % len(personas)]
        persona_content = f"あなたは求職者としてキャリアアドバイザーと会話してください。求職者のペルソナは下記です。\n\n{persona_content}"
        model = models[i % len(models)]
        max_turns = (
            persona_max_turns if persona_max_turns is not None else global_max_turns
        )
        configs.append(
            (
                LLMRepository.get_or_create_model(
                    model["model_name"], model["model_settings"].copy()
                ),
                persona_content,
                max_turns,
                persona_name,
                model["model_name"],
            )
        )

    return configs


async def run_client(
    ws_url: str,
    api_url: str,
    model: Any,
    system_prompt: str,
    max_turns: int,
    client_id: str,
    model_name: str,
    debug_mode: bool = False,
) -> None:
    """
    E2Eクライアントを実行する。

    Args:
        ws_url (str): キャリアアドバイザーサーバーのWebSocket URL
        api_url (str): キャリアアドバイザーサーバーのAPI URL
        model (Any): 求職者LLM model
        system_prompt (str): 求職者システムプロンプト
        max_turns (int): 最大会話数
        client_id (str): キャリアアドバイザークライアントID
        model_name (str): ログ出力用モデル名
        debug_mode (bool): デバッグモード

    Returns:
        None
    """
    print(f"{client_id} ({model_name}) 開始")

    client = E2EClient(
        ws_url,
        api_url,
        model,
        system_prompt,
        max_turns,
        client_id,
        model_name,
        debug_mode,
    )
    result = await client.run()

    print(f"{client_id} ({model_name}) 終了")

    if result:
        stats["conversations"].append(result)


def generate_summary():
    """
    マークダウン形式のサマリを生成します。

    Returns:
        None
    """
    if not stats["conversations"]:
        return

    now = datetime.datetime.now()
    timestamp = now.strftime("%Y%m%d%H%M%S")
    summary_filename = f"summary_{timestamp}.md"
    print(f"サマリ生成開始：{summary_filename}")

    summary = ["# E2Eテスト概要\n"]

    # 全体統計
    all_first_msg_times = []
    all_total_times = []
    all_agent_invoke_times = []

    for conv in stats["conversations"]:
        for stat in conv["stats"]:
            all_first_msg_times.append(stat["first_message_time"])
            all_total_times.append(stat["total_response_time"])
            all_agent_invoke_times.append(stat["agent_invoke_time"])

    summary.append(f"## 全体統計")
    summary.append(f"- 総会話数: {len(stats['conversations'])}")
    summary.append(
        f"- 平均初回メッセージ応答時間: {sum(all_first_msg_times)/len(all_first_msg_times):.2f}s"
    )
    summary.append(
        f"- 平均総応答時間: {sum(all_total_times)/len(all_total_times):.2f}s"
    )
    summary.append(
        f"- 平均エージェント処理時間: {sum(all_agent_invoke_times)/len(all_agent_invoke_times):.2f}s\n"
    )

    # Per-client statistics
    summary.append("## クライアント別統計\n")
    for conv in stats["conversations"]:
        client_stats = conv["stats"]
        first_msg_times = [s["first_message_time"] for s in client_stats]
        total_times = [s["total_response_time"] for s in client_stats]
        agent_invoke_times = [s["agent_invoke_time"] for s in client_stats]

        summary.append(f"### {conv['persona']} ({conv['model']})")
        summary.append(f"- 会話ターン数: {conv['turns']}")
        summary.append(
            f"- 平均初回メッセージ応答時間: {sum(first_msg_times)/len(first_msg_times):.2f}s"
        )
        summary.append(f"- 平均総応答時間: {sum(total_times)/len(total_times):.2f}s")
        summary.append(
            f"- 平均エージェント処理時間: {sum(agent_invoke_times)/len(agent_invoke_times):.2f}s\n"
        )

    # Write summary file
    with open(summary_filename, "w") as f:
        f.write("\n".join(summary))

    print(f"サマリ生成完了：{summary_filename}")


async def main():
    config = load_config()

    client_configs = create_client_configs(config)
    if not client_configs:
        print("config読み込みが失敗しました。")
        return

    ws_url = config["agent_server"]["ws_url"]
    api_url = config["agent_server"]["api_url"]
    run_mode = config["run_mode"]
    if run_mode not in ["DEBUG", "TEST"]:
        print(f"run_modeを正しく設定してください。: {run_mode}")
        return

    try:
        if run_mode == "DEBUG":
            model, system_prompt, max_turns, client_id, model_name = client_configs[0]
            await run_client(
                ws_url,
                api_url,
                model,
                system_prompt,
                max_turns,
                client_id,
                model_name,
                debug_mode=True,
            )
        else:
            tasks = []
            for (
                model,
                system_prompt,
                max_turns,
                client_id,
                model_name,
            ) in client_configs:
                task = run_client(
                    ws_url,
                    api_url,
                    model,
                    system_prompt,
                    max_turns,
                    client_id,
                    model_name,
                )
                tasks.append(task)

            await asyncio.gather(*tasks)
    finally:
        generate_summary()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nExiting...")
