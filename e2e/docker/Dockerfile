FROM python:3.12-slim

ENV APP_HOME=/home/<USER>/e2e
RUN mkdir -p $APP_HOME
WORKDIR $APP_HOME

ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

RUN addgroup --system app && adduser --system --group app

RUN apt-get update \
    && apt-get install -y --no-install-recommends gcc build-essential libpq-dev curl git \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*
COPY ./README.md .
COPY ./pyproject.toml .
RUN pip install --upgrade pip
RUN pip install -e .

COPY ./config.yml $APP_HOME/
COPY ./persona $APP_HOME/persona
COPY ./src $APP_HOME/src
COPY ./start_test.sh $APP_HOME/

RUN chown -R app:app $APP_HOME

USER app

ENTRYPOINT ["/home/<USER>/e2e/start_test.sh"]
