[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "AICA-Client"
version = "0.0.1"
description = "AICAエージェントテストのE2Eクライアント"
readme = "README.md"
requires-python = ">=3.12,<3.13"
classifiers = [
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "aiohttp==3.10.11",
    "backoff==2.2.1",
    "langgraph==0.5.3",
    "langchain==0.3.26",
    "langchain-openai==0.3.27",
    "langchain-aws==0.2.28",
    "pydantic==2.11.7",
    "PyYAML==6.0.2",
    "websockets==15.0.1",
    "aiohappyeyeballs==2.6.1",
    "aiosignal==1.4.0",
    "annotated-types==0.7.0",
    "anyio==4.9.0",
    "attrs==25.3.0",
    "boto3==1.39.11",
    "botocore==1.39.11",
    "certifi==2025.7.14",
    "charset-normalizer==3.4.2",
    "distro==1.9.0",
    "frozenlist==1.7.0",
    "h11==0.16.0",
    "httpcore==1.0.9",
    "httpx==0.28.1",
    "idna==3.10",
    "jiter==0.10.0",
    "jmespath==1.0.1",
    "jsonpatch==1.33",
    "jsonpointer==3.0.0",
    "langchain-core==0.3.71",
    "langchain-text-splitters==0.3.8",
    "langgraph-checkpoint==2.1.1",
    "langgraph-prebuilt==0.5.2",
    "langgraph-sdk==0.1.74",
    "langsmith==0.4.8",
    "multidict==6.6.3",
    "numpy==2.3.1",
    # https://github.com/openai/openai-agents-python/issues/1227
    # e2eに特に影響しないが、serverと一致するため
    "openai==1.95.1",
    "orjson==3.11.0",
    "ormsgpack==1.10.0",
    "packaging==25.0",
    "propcache==0.3.2",
    "pydantic_core==2.33.2",
    "python-dateutil==2.9.0.post0",
    "regex==2024.11.6",
    "requests==2.32.4",
    "requests-toolbelt==1.0.0",
    "s3transfer==0.13.1",
    "six==1.17.0",
    "sniffio==1.3.1",
    "SQLAlchemy==2.0.41",
    "tenacity==9.1.2",
    "tiktoken==0.9.0",
    "tqdm==4.67.1",
    "typing-inspection==0.4.1",
    "typing_extensions==4.14.1",
    "urllib3==2.5.0",
    "xxhash==3.5.0",
    "yarl==1.20.1",
    "zstandard==0.23.0",
]

[tool.hatch.build.targets.wheel]
packages = ["aica_client"]