[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "AICA-Batch"
version = "0.0.1"
description = "AICAエージェントバッチ"
readme = "README.md"
requires-python = ">=3.12,<3.13"
classifiers = [
    "Programming Language :: Python :: 3.12",
    "Operating System :: OS Independent",
]
dependencies = [
    "dependency-injector==4.46.0",
    "psycopg==3.2.6",
    "psycopg-binary==3.2.6",
    "SQLAlchemy==2.0.40",
    "typer==0.16.0",
    "click==8.2.1",
    "markdown-it-py==3.0.0",
    "mdurl==0.1.2",
    "Pygments==2.19.2",
    "PyYAML==6.0.2",
    "rich==14.0.0",
    "shellingham==1.5.4",
    "typing_extensions==4.14.1",
]

[project.urls]
Repository = "https://github.com/MIIDAS-Company/miidas_aica_agent"
Issues = "https://github.com/MIIDAS-Company/miidas_aica_agent/issues"

[tool.hatch.build.targets.wheel]
packages = ["aica_batch"]

[tool.hatch.metadata]
allow-direct-references = true
