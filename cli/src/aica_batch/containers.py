import logging.config
from pathlib import Path

from dependency_injector import containers, providers

from commands.chat_command import ChatCommand
from database import Database


class Container(containers.DeclarativeContainer):
    """
    Dependency Injecttion
    """

    config = providers.Configuration(yaml_files=[str(Path(__file__).parent / "config.yml")])

    _ = providers.Resource(
        logging.config.dictConfig,
        config=config.logging,
    )

    db = providers.Singleton(Database, db_url=config.db.url)

    chat_command_factory = providers.Factory(
        ChatCommand,
        session_factory=db.provided.session,
    )
