from contextlib import AbstractContextManager
from datetime import datetime, timedelta
import logging
from sqlalchemy import text
from sqlalchemy.orm import Session
from typing import Callable

from utils.const import LOGGER_PREFIX


class ChatCommand:
    """
    チャット操作関連コマンド
    """

    def __init__(
        self,
        session_factory: Callable[..., AbstractContextManager[Session]],
    ) -> None:
        """
        インスタンス初期化

        Args:
            session_factory: DBセッションッ
        """
        self._logger = logging.getLogger(
            f"{LOGGER_PREFIX}.{self.__class__.__module__}.{self.__class__.__name__}"
        )
        self._session_factory = session_factory

    def clean_session(self):
        """
        会話履歴クリーニング
        - 30日前のデータをバックアップに移動
        - 2日前のデータを論理削除
        """
        self._logger.info("セッションクリーニング開始")

        with self._session_factory() as session:
            now = datetime.now()
            two_weeks_ago = now - timedelta(days=2)
            one_month_ago = now - timedelta(days=30)

            # 1. 30日前のデータをバックアップに移動してから物理削除
            
            # 1-1. chat_sessionsをバックアップ
            backup_sessions_sql = text("""
                INSERT INTO chat_sessions_backup
                SELECT *
                FROM chat_sessions
                WHERE created_at < :one_month_ago
            """)
            
            backup_sessions_result = session.execute(backup_sessions_sql, {"one_month_ago": one_month_ago})
            backup_sessions_count = backup_sessions_result.rowcount
            self._logger.info(f"バックアップレコード数(chat_sessions): {backup_sessions_count}")
            
            if backup_sessions_count > 0:
                # 1-2. chat_historiesをバックアップ
                backup_histories_sql = text("""
                    INSERT INTO chat_histories_backup
                    SELECT ch.*
                    FROM chat_histories ch
                    INNER JOIN chat_sessions cs ON ch.session_id = cs.session_id
                    WHERE cs.created_at < :one_month_ago
                """)
                
                backup_histories_result = session.execute(backup_histories_sql, {"one_month_ago": one_month_ago})
                backup_histories_count = backup_histories_result.rowcount
                self._logger.info(f"バックアップレコード数(chat_histories): {backup_histories_count}")
                
                # 1-3. user_profilesをバックアップ
                backup_profiles_sql = text("""
                    INSERT INTO user_profiles_backup
                    SELECT up.*
                    FROM user_profiles up
                    INNER JOIN chat_sessions cs ON up.session_id = cs.session_id
                    WHERE cs.created_at < :one_month_ago
                """)
                
                backup_profiles_result = session.execute(backup_profiles_sql, {"one_month_ago": one_month_ago})
                backup_profiles_count = backup_profiles_result.rowcount
                self._logger.info(f"バックアップレコード数(user_profiles): {backup_profiles_count}")
            
            # 物理削除
            physical_delete_sql = text("""
                SELECT COUNT(*) FROM chat_sessions 
                WHERE created_at < :one_month_ago
            """)
            physical_delete_count = session.execute(physical_delete_sql, {"one_month_ago": one_month_ago}).scalar()
            self._logger.info(f"物理削除セッション数: {physical_delete_count}")
            
            delete_sessions_sql = text("""
                DELETE FROM chat_sessions 
                WHERE created_at < :one_month_ago
            """)
            session.execute(delete_sessions_sql, {"one_month_ago": one_month_ago})

            # 2. 2週間前のデータを論理削除
            logical_delete_sql = text("""
                SELECT COUNT(*) FROM chat_sessions 
                WHERE created_at < :two_weeks_ago AND deleted_at IS NULL
            """)
            logical_delete_count = session.execute(logical_delete_sql, {"two_weeks_ago": two_weeks_ago}).scalar()
            self._logger.info(f"論理削除セッション数: {logical_delete_count}")
            
            update_sessions_sql = text("""
                UPDATE chat_sessions 
                SET deleted_at = :now 
                WHERE created_at < :two_weeks_ago AND deleted_at IS NULL
            """)
            session.execute(update_sessions_sql, {"two_weeks_ago": two_weeks_ago, "now": now})
            session.commit()

        self._logger.info("セッションクリーニング完了")
