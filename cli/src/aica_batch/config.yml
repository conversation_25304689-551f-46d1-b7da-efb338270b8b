logging:
  version: 1
  disable_existing_loggers: False
  formatters:
    formatter:
      format: "[%(asctime)s] [%(levelname)s] [%(name)s]: %(message)s"
  handlers:
    console:
      class: "logging.StreamHandler"
      formatter: "formatter"
      stream: "ext://sys.stdout"
    file:
      class: "logging.FileHandler"
      level: "DEBUG"
      formatter: "formatter"
      filename: "/tmp/aica_batch.log"
      mode: "a"
  root:
    level: "INFO"
    handlers:
      - console
      - file
  loggers:
    aica_batch:
      level: "DEBUG"
      propagate: True
db:
  url: "postgresql+psycopg://${AICA_AGENT_DB_USER}:${AICA_AGENT_DB_PASSWORD}@${AICA_AGENT_DB_HOST}:${AICA_AGENT_DB_PORT}/${AICA_AGENT_DB_NAME}?sslmode=${AICA_AGENT_DB_SSLMODE}"
