# AICA Batch CLI

[typer](https://github.com/fastapi/typer)を使ってバッチコマンドを提供します。

## コマンド一覧

### `clean_session`

非会員の場合、下記の処理を行います。

- 論理削除：2日（会員になったら復元できる）
- 物理削除：1ヶ月（会員になっても復元できない）

#### TODO

会員関連の設計／実装はまだなので、いまセッションクリーニングのときに、会員・非会員の判断ロジックは入っていません。

# ローカルでの起動

## 事前準備

### DB構築

[aica_db_migrationsリポジトリ](https://github.com/MIIDAS-Company/aica_db_migrations)のREADMEを参照

### 環境変数

- `.env.example`を`.env.local`にコピーし、値を入れてください。
- `127.0.0.1	pgvector`を事前に`/etc/hosts`にいれるとVSCodeとコンテナ内と同じエンドポイントが利用できます。

### イメージ作成

```bash
cd cli
docker build -f docker/Dockerfile . -t agent-batch:latest
```

## 実行コマンド

### `clean_session`

```bash
cd cli
docker run -it --rm --env-file .env.local --network ai-ca_default agent-batch:latest clean_session
```

# 開発者向け

## プロジェクト構造

- src/aica_batch/commands
  - 具体的なコマンド実装
- docker
  - イメージ作成・起動用

## 主に利用しているライブラリ

- typer
  - Python CLI作成
- psycopg、SQLAlchemy
  - DBアクセス
- dependency-injector
  - 依存性注入（Dependency Injection: DI）

## デバッグ

### 準備

全体`README`参照

### 起動方法

VSCodeで`launch.json`の`[Batch]Local Debug`を実行してください。
