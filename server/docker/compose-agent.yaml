name: ai-ca
services:
  agent-server:
    container_name: agent-server
    build:
      context: ..
      dockerfile: docker/Dockerfile
    command: gunicorn application:app -c gunicorn_config.py
    ports:
      - "${AICA_AGENT_PORT}:${AICA_AGENT_PORT}"
    environment:
      - PYTHONPATH=/home/<USER>/web
      - TZ=Asia/Tokyo
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - AICA_AGENT_PORT=${AICA_AGENT_PORT}
      - AICA_AGENT_DB_HOST=${AICA_AGENT_DB_HOST}
      - AICA_AGENT_DB_PORT=${AICA_AGENT_DB_PORT}
      - AICA_AGENT_DB_USER=${AICA_AGENT_DB_USER}
      - AICA_AGENT_DB_PASSWORD=${AICA_AGENT_DB_PASSWORD}
      - AICA_AGENT_DB_NAME=${AICA_AGENT_DB_NAME}
      - AICA_AGENT_DB_SSLMODE=${AICA_AGENT_DB_SSLMODE}
      - AICA_AGENT_MCP_ENDPOINT=${AICA_AGENT_MCP_ENDPOINT}
      - AICA_AGENT_API_ENDPOINT=${AICA_AGENT_API_ENDPOINT}
      - OPENAI_AGENTS_DISABLE_TRACING=${OPENAI_AGENTS_DISABLE_TRACING}
    volumes:
      - ../src/aica_agent:/home/<USER>/web/:ro
      - ../tests:/home/<USER>/tests
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost/agent/health || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 5

  agent-server-debug:
    extends: agent-server
    command: python -m debugpy --listen 0.0.0.0:5678 --wait-for-client -m uvicorn application:app --reload --host 0.0.0.0 --port ${AICA_AGENT_PORT} --limit-concurrency 100 --timeout-keep-alive 30
    ports:
      - "${AICA_AGENT_PORT}:${AICA_AGENT_PORT}"
      - 5678:5678
