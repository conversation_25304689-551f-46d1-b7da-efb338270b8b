FROM python:3.12-slim

ENV APP_HOME=/home/<USER>/web
RUN mkdir -p $APP_HOME
WORKDIR $APP_HOME

ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
# disable frozen modules for debugging
ENV PYDEVD_DISABLE_FILE_VALIDATION=1

RUN addgroup --system app && adduser --system --group app

RUN apt-get update \
    && apt-get install -y --no-install-recommends gcc build-essential libpq-dev curl git \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

RUN pip install debugpy
COPY ./README.md .
COPY ./pyproject.toml .
RUN pip install --upgrade pip
RUN pip install ".[test]"

RUN chown -R app:app $APP_HOME

USER app
