# syntax=docker/dockerfile:1
ARG PYTHON_VERSION=3.12
FROM python:${PYTHON_VERSION}-slim

ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

ENV APP_HOME=/home/<USER>/web
RUN mkdir -p $APP_HOME
WORKDIR $APP_HOME

RUN addgroup --system app && adduser --system --group app

RUN apt-get update \
    && apt-get install -y --no-install-recommends gcc build-essential libpq-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

COPY ./README.md .
COPY ./pyproject.toml .

RUN pip install --upgrade pip
RUN pip install .

RUN rm -rf ./README.md

COPY ./src/aica_agent $APP_HOME
