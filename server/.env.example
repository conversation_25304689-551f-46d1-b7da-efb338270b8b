OPENAI_API_KEY=
# ローカル利用する定義
AICA_AGENT_PORT=8000
AICA_AGENT_DB_HOST=pgvector
AICA_AGENT_DB_PORT=5432
AICA_AGENT_DB_USER=postgres
AICA_AGENT_DB_PASSWORD=postgres
AICA_AGENT_DB_NAME=postgres
AICA_AGENT_DB_SSLMODE=disable
AICA_AGENT_MCP_ENDPOINT=http://mcp-server:8080/mcp
AICA_AGENT_API_ENDPOINT=http://api-server:10001/aica/mcptool/
AICA_AGENT_MIIDAS_API_ENDPOINT=http://localhost:4000/miidas/

# bedrockを利用するときのみ
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_SESSION_TOKEN=
AWS_REGION_NAME=

# disable OpenAI Agents tracing
OPENAI_AGENTS_DISABLE_TRACING=1
# disable LiteLLM debug log
OPENAI_AGENTS_DONT_LOG_MODEL_DATA=1
