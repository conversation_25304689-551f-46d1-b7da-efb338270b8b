import pytest
from unittest.mock import AsyncMock, Mock

from repositories.api_repo import AICAAPIRepository
from repositories.position_repo import PositionRepository
from services.position_service import PositionService

# PositionRepositoryをモック化
@pytest.fixture
def mock_position_repository():
    return Mock(spec=PositionRepository)

# APIRepositoryをモック化
@pytest.fixture
def mock_api_repository():
    return AsyncMock(spec=AICAAPIRepository)

@pytest.mark.asyncio
async def test_get_position_detail(mock_position_repository, mock_api_repository):
    # PositionServiceのインスタンスを生成し、依存を注入
    position_service = PositionService(mock_position_repository, mock_api_repository, "http://fakeapi")

    # 各モックメソッドの戻り値を設定
    mock_position_repository.decrypt_id.return_value = "decrypted_position_id"
    mock_api_repository.post.return_value = {"Position": "Test Position Data"}
    mock_position_repository.save_position_detail.return_value = None

    # テスト実行
    result = await position_service.get_position_detail("encrypted_position_id")

    # 結果を確認
    assert result == {"Position": "Test Position Data"}

    # モックのメソッドが一度だけ特定の引数で呼び出されたことを確認
    mock_position_repository.decrypt_id.assert_called_once_with("encrypted_position_id")
    mock_api_repository.post.assert_called_once_with("http://fakeapi/positions/detail/decrypted_position_id")
    mock_position_repository.save_position_detail.assert_called_once_with("encrypted_position_id", {"Position": "Test Position Data"})
