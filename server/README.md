# 概要

キャリアアドバイザーAIのサーバーです。

AIと会話するためのWebsocket接続、ポジション情報関連のRestful APIを提供します。

## Websocketエンドポイント

- キャリアアドバイザーと会話する
  - `/chat`

## RestFul APIエンドポイント

- ヘルスチェック
  - `/health`
- ポジション検索のもっと見る
  - `/positions/search/{search_key}/{offset}`
- ポジション検索のおすすめ
  - `/positions/recommendations/{search_key}/{encrypted_theme}`
- ポジション詳細
  - `/positions/{encrypted_position_id}`
- 会社詳細
  - `/companies/{encrypted_position_id}`
- 業界詳細
  - `/businesses/{encrypted_position_id}`

# ローカルでの起動

## 事前準備

### DB構築

[aica_db_migrationsリポジトリ](https://github.com/MIIDAS-Company/aica_db_migrations)のREADMEを参照

### 環境変数

- `.env.example`を`.env.local`にコピーしし、値を入れてください。
- `127.0.0.1	pgvector mcp-server api-server`を事前に`/etc/hosts`にいれるとVSCodeとコンテナ内と同じエンドポイントが利用できます。

## 起動コマンド

`./start_server.sh`

## 検証方法

- 基本フロントを起動してブラウザからアクセスして、Agentサーバの機能を確認できます。
- Restful APIの確認は特に開発のときに何回も行うことがあるので、会話しながらLLMにツールを呼び出してもらう必要があるので、面倒かもしれない。
  - そのため、`Agentサーバ.postman_collection.json`を使って確認するのは可能ですが、その前に、一度キャリアアドバイザーとの会話でポジション検索が実行される必要があります。その後、APIに必要な情報：
    - HTTPヘッダーのセッションID。
      - ブラウザのローカルストレージから取得可能
    - リクエストパラメータ
      - search_key: フロント側は複数検索のある場合、区別に利用されうものです。任意で良い。
      - その他: Websocketのポジション検索レスポンスから取得可能

### `Agentサーバ.postman_collection.json`使い方

#### Postmanインストール

[ここ](https://www.postman.com/downloads/?deviceId=698c1d4d-313f-458e-9700-35f1b77045f2)からダウンロードできます。

#### APIデータインポート

`Agentサーバー.postman_collection.json`をPostmanにインポートしたら、利用できます。

# 開発者向け

## プロジェクト構造

- src/aica_agent/services
  - 業務ロジック
    - chat_service.py
      - LLM会話関連(Websocketエンドポイント処理)
    - position_service.py
      - ポジション関連（ポジション検索、詳細取得などのRestful APIエンドポイント処理）
- src/aica_agent/repositories
  - レポジトリ（DB、メモリ、LLM操作）
- src/aica_agent/domain/entities
  - モデル定義（テーブルORM）
- src/aica_agent/utils
  - 定数、ヘルパークラス、メソッド
- docker
  - コンテナ起動用

## 主に利用しているライブラリ
- openai-agents、mcp、boto3
  - AI関連
- uvicorn、gunicorn、fastapi
  - Websocket、Restful APIサーバー
- psycopg、SQLAlchemy
  - DBアクセス
- dependency-injector
  - 依存性注入（Dependency Injection: DI）

## デバッグ

### コンテナで起動する場合

コンテナで起動されるサービスをデバッグする方法なので、ローカルでのPythonインストールは不要です。

VSCodeで`launch.json`の`[Agent]Remote Debug`を実行すればコンテナでAgentサーバーを起動し、デバッグできます。

#### 備考

デバッグにはポート5678を利用していますので、`lsof -i:5678`で他にポート5678を利用しているサービスがないかを確認してください。

もしあったら、そのポートを解放するか、下記ファイルのデバッグポートを変えてください。
- .vscode/launch.json
- server/docker/compose-agent.yaml

### VSCodeで起動する場合

#### 準備

全体`README`参照

#### 起動方法

VSCodeで`launch.json`の`[Agent]Local Debug`を実行すればAPIサーバーを起動し、デバッグできます。

## 単体テスト

### 概要
単体テストにはpytestとTestClientを使用しており、テストコードは`tests/unit/`に配置しています。

### 実行方法

以下コマンドで実行
```
docker exec -it agent-server bash -c "cd ../tests/unit/ && pytest"
```

### pytestオプション

テスト結果の詳細を出力
```
pytest -v
```

特定のファイルを実行
```
pytest ファイルパス
```

特定のテスト関数を実行
```
pytest ファイルパス::関数名
```

カバレッジ出力
```
pytest --cov
```

カバレッジレポート出力（HTML）
```
pytest --cov --cov-report=html
```
htmlcovというフォルダが作成され、その中のindex.htmlで確認できます

※その他の出力形式は[ドキュメント](https://pytest-cov.readthedocs.io/en/latest/reporting.html)をご確認ください

## その他

### LiteLLM

OpenAI Agentだけで、LiteLLMを利用しない場合、OpenAIはデフォルトでResponse APIを利用しますが、

OpenAI Agent経由でLiteLLMを使ったら、Chat Completion APIしか使えなさそうです。

[ここ](https://docs.litellm.ai/docs/providers/openai/responses_api)を見ると、OpenAI Agentを使わず、直接にLiteLLMを利用する場合、OpenAIのResponse APIが使えそうです。

#### 定義済みモデル

1. bedrock/anthropic.claude-3-5-sonnet-20240620-v1
2. openai/gpt-4.1

定義詳細は「src/aica_agent/config.yml」参照

#### モデルの追加

1. `src/aica_agent/config.yml`にモデルの定義を追加
2. `src/aica_agent/repositories/llm_repo.py`の`LLMModel`にモデルの定義を追加
3. `src/aica_agent/repositories/llm_repo.py`の`active_agent()`にモデル選択分岐を追加

#### モデル定義の追加・変更

`src/aica_agent/config.yml`のmodel_listの中身を修正すれば良い。

#### 利用モデルの指定

リクエストパラメータで直接指定を避けるため、`src/aica_agent/endpoints.py`の`handle_chat_session`をいじって、サーバ再起動が必要となります。
