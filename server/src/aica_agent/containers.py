"""
依存性注入（Dependency Injection: DI）
"""
import logging.config

from dependency_injector import containers, providers

from database import Database
from repositories.llm_repo import LLMRepository
from repositories import agent_repo, chat_repo, position_repo, user_repo, api_repo
from services import chat_service, position_service, user_service
from utils.logging import record_factory
from utils import session_key_manager


class Container(containers.DeclarativeContainer):
    wiring_config = containers.WiringConfiguration(
        modules=["endpoints", "utils.fastapi.dependency"]
    )

    config = providers.Configuration(yaml_files=["config.yml"])

    logging.setLogRecordFactory(record_factory)
    _ = providers.Resource(
        logging.config.dictConfig,
        config=config.logging,
    )

    db = providers.Singleton(Database, db_url=config.db.url)
    session_key_manager = providers.Singleton(session_key_manager.SessionKeyManager)

    agent_repository = providers.Factory(
        agent_repo.AgentRepository,
        session_factory=db.provided.session,
    )

    chat_repository = providers.Factory(
        chat_repo.ChatRepository,
        session_factory=db.provided.session,
    )

    user_repository = providers.Factory(
        user_repo.UserRepository,
        session_factory=db.provided.session,
    )

    llm_repository = providers.Resource(
        LLMRepository,
        mcp_url=config.mcp.url,
        model_list=config.model_list,
        agent_repository=agent_repository,
    )

    # 実際のデータを持っているので、Singletonにしました。
    position_repository = providers.Singleton(
        position_repo.PositionRepository,
        session_key_manager=session_key_manager,
    )

    # 異なるユーザーが会員登録＆面談応募するので、本体側へのHTTPリクエストは全部１つのセッションで行っていけない。
    # AICA APIサーバへのリクエストはステートレスなので、１つのセッションで大丈夫です。
    aica_api_repository = providers.Resource(
        api_repo.AICAAPIRepository,
        timeout=config.api.timeout,
        aica_api_url=config.api.aica,
    )

    chat_service = providers.Factory(
        chat_service.ChatService,
        llm_repository=llm_repository,
        chat_repository=chat_repository,
        position_repository=position_repository,
        user_repository=user_repository,
        session_key_manager=session_key_manager,
    )

    position_service = providers.Factory(
        position_service.PositionService,
        position_repository=position_repository,
        aica_api_repository=aica_api_repository,
    )

    user_service = providers.Factory(
        user_service.UserService,
        chat_repository=chat_repository,
        user_repository=user_repository,
        position_repository=position_repository,
        aica_api_repository=aica_api_repository,
        miidas_api_url=config.api.miidas,
        timeout=config.api.timeout,
    )
