from contextlib import Abstract<PERSON>ontextManager
from typing import Callable, <PERSON><PERSON>, Optional
from sqlalchemy import select, func
from sqlalchemy.orm import Session, joinedload, aliased

from domain.entities.chat_history import ChatHistory
from domain.entities.chat_session import ChatSession, ChatSessionStatus
from domain.entities.user_profile import UserProfile
from utils.crypt import create_secret_key
from utils.logging import get_session_id


class ChatRepository:
    def __init__(
        self,
        session_factory: Callable[..., AbstractContextManager[Session]],
    ) -> None:
        self._session_factory = session_factory

    def init_chat_session(self) -> <PERSON><PERSON>[Optional[ChatSession], bool]:
        """
        会話履歴とユーザー検索条件取得

        Args:
            None

        Returns:
            会話履歴 or None
        """
        session_id = get_session_id()
        with self._session_factory() as session:
            HistoryAlias = aliased(ChatHistory)
            ProfileAlias = aliased(UserProfile)
            stmt = (
                select(ChatSession)
                .options(
                    joinedload(
                        ChatSession.histories.of_type(HistoryAlias).and_(
                            HistoryAlias.deleted_at.is_(None)
                        )
                    ),
                    joinedload(
                        ChatSession.user_profile.of_type(ProfileAlias).and_(
                            ProfileAlias.deleted_at.is_(None)
                        )
                    ),
                )
                .filter(
                    ChatSession.session_id == session_id,
                    ChatSession.deleted_at.is_(None),
                )
                .order_by(HistoryAlias.id)
            )
            chat_session = session.scalars(stmt).unique().first()
            if chat_session:
                # まだ生きているセッションがある場合
                if not chat_session.secret_key:
                    secret_key = create_secret_key()
                    chat_session.secret_key = secret_key.decode()
                    session.add(chat_session)
                    session.commit()
                return (chat_session, True)
            else:
                # 指定session_idのセッションは期限切れで削除されたかを確認します。
                count_stmt = (
                    select(func.count())
                    .select_from(ChatSession)
                    .where(
                        ChatSession.session_id == session_id,
                        ChatSession.deleted_at.isnot(None),
                    )
                )
                count = session.execute(count_stmt).scalar_one()
                return (None, count > 0)

    def create_chat_session(
        self,
        secret_key: str,
    ):
        """
        会話セッション作成する。

        Args:
            secret_key: 秘密鍵
        """
        with self._session_factory() as session:
            chat_session = ChatSession(
                session_id=get_session_id(),
                secret_key=secret_key,
            )
            session.add(chat_session)
            session.commit()

    def add_chat_histories(
        self,
        chat_histories: list[ChatHistory],
    ):
        """
        複数会話を追加する。

        Args:
            chat_histories: 会話履歴
        """
        with self._session_factory() as session:
            for chat_history in chat_histories:
                session.add(chat_history)
                session.commit()

    def add_chat_history(
        self,
        chat_history: ChatHistory,
    ) -> None:
        """
        １つの会話を追加する。

        Args:
            chat_history: 会話履歴
        """
        with self._session_factory() as session:
            session.add(chat_history)
            session.commit()

    def session_status(
        self,
    ) -> ChatSessionStatus | None:
        """
        該当セッションのステータス

        Args:
            None

        Returns:
            セッション存在する場合、status
            存在してない場合、None
        """
        session_id = get_session_id()
        with self._session_factory() as session:
            stmt = select(ChatSession).filter(
                ChatSession.session_id == session_id,
                ChatSession.deleted_at.is_(None),
            )
            chat_session = session.scalars(stmt).first()
            if chat_session:
                return chat_session.status
            return None

    def update_session_status(
        self,
        session_status: ChatSessionStatus,
    ) -> ChatSessionStatus | None:
        """
        該当セッションのステータスを更新する

        Args:
            session_status: 新しいステータス

        Returns:
            セッション存在する場合、新しいステータス
            存在してない場合、None
        """
        session_id = get_session_id()
        with self._session_factory() as session:
            stmt = select(ChatSession).filter(
                ChatSession.session_id == session_id,
                ChatSession.deleted_at.is_(None),
            )
            chat_session = session.scalars(stmt).first()
            if chat_session:
                chat_session.status = session_status
                session.commit()
                return session_status
            return None

    def get_position_chat_histories(
        self,
        position_id: str,
    ) -> list[ChatHistory]:
        """
        ポジション会話履歴を取得する

        Args:
            position_id: ポジションID

        Returns:
            会話履歴のリスト
        """
        session_id = get_session_id()

        with self._session_factory() as session:
            stmt = (
                select(ChatHistory)
                .filter(
                    ChatHistory.session_id == session_id,
                    ChatHistory.position_id == int(position_id),
                    ChatHistory.deleted_at.is_(None),
                )
                .order_by(ChatHistory.id)
            )

            return list(session.scalars(stmt))
