from enum import St<PERSON><PERSON><PERSON>
from typing import <PERSON>ple
from copy import deepcopy
from agents import Agent, ModelSettings
from dependency_injector import resources
import logging

import aiohttp
import json
import uuid
from openai.types.shared import Reasoning

from repositories.agent_repo import AgentRepository
from utils.const import LOGGER_PREFIX


class NotSupportedModelName(Exception):
    """
    定義されていないモデル
    """

    def __init__(self, message):
        super().__init__(message)


class AgentName(StrEnum):
    """
    エージェント名
    テーブル[agents]のカラム[name]となります。
    動的にアクティブエージェントを切り替えるときに利用するためソースにも定義しています。
    """

    DEFAULT_AGENT = "DefaultAgent"
    CAREER_ADVISOR = "CareerAdvisor"
    POSITION_GUIDE = "PositionGuide"
    POSITION_SEARCH = "PositionSearch"
    POSITION_CHANGE_ANALYZE = "PositionChangeAnalyze"


class LLMRepository(resources.AsyncResource):
    def __init__(self) -> None:
        self._logger = logging.getLogger(
            f"{LOGGER_PREFIX}.{self.__class__.__module__}.{self.__class__.__name__}"
        )

        self._mcp_url = None
        self._agents: dict[str, dict[str, tuple[Agent, bool]]] = {}
        self._tools_cache = None  # Cache tools to avoid repeated initialization calls

    async def init(
        self,
        mcp_url,
        model_list,
        agent_repository: AgentRepository,
    ):
        """
        LLMRepository初期化（ステートレス）
        ・MCPサーバーURL保存
        ・ワークフロー初期化（エージェントとハンドオフ関係、ツール）

        Args:
            mcp_url: MCPサーバーURL
            model_list: LLMモデル一覧
            agent_repository: エージェントリポジトリ
        """
        self._mcp_url = mcp_url
        try:
            await self._init_agents(model_list, agent_repository)
        except Exception as e:
            self._logger.exception(f"Agent初期化失敗（MCPサーバー： {mcp_url}）: {e}")
            raise

        return self

    async def _make_stateless_mcp_request(self, method: str, params: dict = None):
        """
        ステートレスMCPリクエストを送信

        Args:
            method: JSON-RPC メソッド名
            params: リクエストパラメータ

        Returns:
            レスポンス結果
        """
        request_id = str(uuid.uuid4())

        # JSON-RPC リクエストを構築
        json_rpc_request = {
            "jsonrpc": "2.0",
            "id": request_id,
            "method": method,
            "params": params or {}
        }

        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "MCP-Protocol-Version": "2025-06-18"
        }

        async with aiohttp.ClientSession() as session:
            try:
                async with session.post(
                    self._mcp_url,
                    json=json_rpc_request,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        if "error" in result:
                            raise Exception(f"MCP Error: {result['error']}")
                        return result.get("result")
                    else:
                        error_text = await response.text()
                        raise Exception(f"HTTP Error: {response.status} - {error_text}")
            except Exception as e:
                self._logger.error(f"Stateless MCP request failed for {method}: {e}")
                raise

    async def _initialize_mcp_session(self):
        """
        MCPサーバーとの初期化（ステートレス）
        """
        try:
            # Initialize request
            init_result = await self._make_stateless_mcp_request(
                "initialize",
                {
                    "protocolVersion": "2025-06-18",
                    "capabilities": {
                        "tools": {}
                    },
                    "clientInfo": {
                        "name": "AICA Agent",
                        "version": "1.0.0"
                    }
                }
            )

            # Send initialized notification
            await self._make_stateless_mcp_request("notifications/initialized")

            return init_result
        except Exception as e:
            self._logger.error(f"MCP initialization failed: {e}")
            raise

    async def _get_tools_stateless(self):
        """
        ステートレスでMCPサーバーからツール一覧を取得
        """
        if self._tools_cache is not None:
            return self._tools_cache

        try:
            # Initialize session first
            await self._initialize_mcp_session()

            # Get tools list
            tools_result = await self._make_stateless_mcp_request("tools/list")

            # Convert MCP tools to Agent SDK format
            from agents import FunctionTool
            tools = []
            if tools_result and "tools" in tools_result:
                for mcp_tool in tools_result["tools"]:
                    # Create FunctionTool compatible with Agent SDK
                    tool = FunctionTool(
                        name=mcp_tool["name"],
                        description=mcp_tool.get("description", ""),
                        parameters=mcp_tool.get("inputSchema", {}),
                        function=self._create_stateless_tool_function(mcp_tool["name"])
                    )
                    tools.append(tool)

            self._tools_cache = tools
            return tools

        except Exception as e:
            self._logger.error(f"Failed to get tools stateless: {e}")
            raise

    def _create_stateless_tool_function(self, tool_name: str):
        """
        ステートレスツール実行関数を作成

        Args:
            tool_name: ツール名

        Returns:
            ツール実行関数
        """
        async def stateless_tool_function(**kwargs):
            return await self.call_tool_stateless(tool_name, kwargs)

        return stateless_tool_function

    async def call_tool_stateless(self, tool_name: str, arguments: dict):
        """
        ステートレスMCPリクエストでツールを呼び出す

        Args:
            tool_name: ツール名
            arguments: ツール引数

        Returns:
            ツール実行結果
        """
        try:
            # MCP tools/call request
            result = await self._make_stateless_mcp_request(
                "tools/call",
                {
                    "name": tool_name,
                    "arguments": arguments
                }
            )

            # Extract content from MCP response
            if result and "content" in result:
                # MCP returns content as array of content blocks
                content_blocks = result["content"]
                if content_blocks and len(content_blocks) > 0:
                    # Return the first content block's text
                    first_block = content_blocks[0]
                    if "text" in first_block:
                        return first_block["text"]
                    elif "data" in first_block:
                        return first_block["data"]
                    else:
                        return first_block

            return result

        except Exception as e:
            self._logger.error(f"Stateless tool call failed for {tool_name}: {e}")
            raise

    async def shutdown(self, _: None):
        """
        LLMRepository終了処理（ステートレス）
        ・キャッシュクリア
        """
        self._tools_cache = None
        self._agents.clear()
        self._logger.info("LLMRepository shutdown completed (stateless mode)")

    async def _init_agents(
        self,
        model_list,
        agent_repository: AgentRepository,
    ):
        """
        src/aica_agent/config.ymlの[model_list]をもとにワークフロー初期化（エージェントとハンドオフ関係、ツール）

        Args:
            model_list: LLMモデル一覧。src/aica_agent/config.ymlの[model_list]
            agent_repository: エージェントリポジトリ（エージェント設定；デフォルトエージェントや紐づいたツール名など）
        """
        tools = await self._get_tools_stateless()
        tool_names = [tool.name for tool in tools]
        self._logger.debug(f"MCP Tools: {tool_names}")
        agents = agent_repository.get_agents()
        for model in model_list:
            model_name = model["model"]
            raw_settings = deepcopy(model["model_settings"])
            reasoning = raw_settings.get("reasoning")
            if isinstance(reasoning, dict):
                raw_settings["reasoning"] = Reasoning.model_validate(reasoning)
            model_settings = ModelSettings(
                **raw_settings,
            )
            react_agents = {}
            for agent in agents:
                self._logger.debug(
                    f"Agent {agent.name}のシステムプロンプト: {agent.prompt}"
                )

                agent_tool_names = [agent_tool.tool.name for agent_tool in agent.tools]
                non_existent_tools = [
                    tool_name
                    for tool_name in agent_tool_names
                    if tool_name not in tool_names
                ]
                if non_existent_tools:
                    # DB定義ミスった場合、起動エラー
                    self._logger.error(
                        f"Agent {agent.name}のツール{non_existent_tools}がDBに存在しない。"
                    )
                    raise Exception(
                        f"Agent {agent.name}のツール{non_existent_tools}がDBに存在しない。"
                    )
                agent_tools = [tool for tool in tools if tool.name in agent_tool_names]
                react_agent = Agent(
                    model=model_name,
                    model_settings=model_settings,
                    name=agent.name,
                    instructions=agent.prompt,
                    tools=agent_tools,
                )
                stop_at_tool_names = [
                    tool.tool_name for tool in agent.tools if tool.return_direct
                ]
                if stop_at_tool_names:
                    react_agent.tool_use_behavior = {
                        "stop_at_tool_names": stop_at_tool_names,
                    }

                react_agents[agent.name] = (
                    react_agent,
                    agent.next_agents,
                    agent.default_agent,
                )

            for _, (agent, next_agents, _) in react_agents.items():
                if next_agents:
                    agent.handoffs = [
                        react_agents[next_agent.dest_agent.name][0]
                        for next_agent in next_agents
                    ]

            self._agents[model_name] = {
                agent_name: (agent, default_agent)
                for agent_name, (agent, _, default_agent) in react_agents.items()
            }

    def clone_agents(self, model_name: str) -> dict[str, Tuple[Agent, bool]]:
        """
        エージェント群をクローンする。

        Args:
            model_name: LLMモデルネーム

        Returns:
            エージェント群
        """
        if model_name not in self._agents:
            self._logger.exception(f"Unsupported model name: {model_name}")
            raise NotSupportedModelName(f"Unsupported model name: {model_name}")

        # https://miidas-dev.slack.com/archives/C08CPHXCZ08/p1750994467102159
        # 性能面（あくまで推測、負荷テストで確認する必要があります）やThread-safeから考えると、
        # グローバル１つのAgentではなく、セッションごとに各自のAgentを持つ
        cloned_agents = {
            k: (v[0].clone(), v[1]) for k, v in self._agents[model_name].items()
        }
        return cloned_agents
