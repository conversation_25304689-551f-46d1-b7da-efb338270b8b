import copy
import logging
from typing import Any

from utils.const import LOGGER_PREFIX
from utils.crypt import create_secret_key, decrypt, encrypt
from utils.logging import get_session_id
from utils.session_key_manager import SessionKeyManager
from utils import sync_dict


# 本当のポジションIDを外部公開しないため、ポジションツール検索結果をメモリに保存し、ユーザーがポジションの「詳細を見る」を押すときに、ここから本当のポジションIDを取得し、APIサーバからポジション詳細を取得する。
# TODO: セッション再開の場合、もし以前の履歴はまだ見れるなら、表示するたびに、このマッピングを作成する必要があります。
# TODO: エラーがあって接続が切れて再属した場合、セッションはクリアされたので、ポジションカードは画面に表示されていても、実はクリックできない。
class PositionRepository:
    """
    セッション毎にポジション関連情報のキャッシュ
    """

    def __init__(
        self,
        session_key_manager: SessionKeyManager,
    ):
        """
        下記のポジション情報キャッシュ初期化
        ・暗号化・復号化のキー（セッション毎異なる）
        ・ポジション検索結果
        ・ポジション詳細表示に関連する情報
            ・ポジション詳細
            ・会社詳細
            ・業界詳細
        ・ユーザー検索条件
        """
        self._logger = logging.getLogger(
            f"{LOGGER_PREFIX}.{self.__class__.__module__}.{self.__class__.__name__}"
        )
        self._session_key_manager = session_key_manager
        self._position_search_result_ids = sync_dict.SynchronizedDict[
            str, dict[str, list[int]]
        ]()
        self._position_details = sync_dict.SynchronizedDict[str, dict[str, dict]]()
        self._company_details = sync_dict.SynchronizedDict[str, dict[str, dict]]()
        self._business_details = sync_dict.SynchronizedDict[str, dict[str, dict]]()
        self._user_preferences = sync_dict.SynchronizedDict[str, dict[str, Any]]()

    def _encrypt_id(self, real_id: str) -> str:
        """
        ポジションIDを暗号化する

        Args:
            real_id: 実際のポジションID

        Returns:
            暗号化されたポジションID
        """
        key = self._session_key_manager.get_or_create_session_key()
        return encrypt(key, real_id)

    def decrypt_id(self, encrypted_id: str) -> str:
        """
        ポジションIDを復号化する

        Args:
            encrypted_id: 暗号化されたポジションID

        Returns:
            復号化されたポジションID
        """
        key = self._session_key_manager.get_or_create_session_key()
        return decrypt(key, encrypted_id)

    def save_search_result_position_ids(
        self,
        search_key: str,
        position_ids: list[int],
    ) -> int:
        """
        ポジション検索結果IDをキャッシュする

        Args:
            search_key: 検索キー
            position_ids: 検索結果のポジションIDリスト

        Returns:
            ポジション検索結果数
        """
        if not position_ids:
            return

        seen = set()
        unique_position_ids = []
        for pid in position_ids:
            if pid not in seen:
                seen.add(pid)
                unique_position_ids.append(pid)

        session_data = self._position_search_result_ids.setdefault(get_session_id(), {})
        session_data[search_key] = unique_position_ids

        return len(unique_position_ids)

    def get_search_result_position_ids(
        self,
        search_key: str,
        offset: int,
        limit: int,
    ) -> list[int]:
        """
        ポジション検索結果IDを取得

        Args:
            search_key: 検索キー

        Returns:
            ポジション検索結果IDリスト
        """
        session_data = self._position_search_result_ids.setdefault(get_session_id(), {})
        return session_data.get(search_key, [])[offset : offset + limit]

    def get_search_result_count(self, search_key: str) -> int:
        """
        ポジション検索結果数を取得

        Args:
            search_key: 検索キー

        Returns:
            ポジション検索結果数
        """
        session_data = self._position_search_result_ids.setdefault(get_session_id(), {})
        return len(session_data.get(search_key, []))

    def remove_search_result_positions_ids(
        self,
        search_key: str,
        position_ids: list[int],
    ) -> int:
        """
        ポジション検索結果IDを削除

        Args:
            search_key: 検索キー
            position_ids: 削除するポジションIDリスト
        """
        session_data = self._position_search_result_ids.setdefault(get_session_id(), {})
        if search_key in session_data:
            session_data[search_key] = [
                pid for pid in session_data[search_key] if pid not in position_ids
            ]

        return len(session_data.get(search_key, []))

    def process_and_cache_positions(
        self,
        positions: list[dict[str, Any]],
    ) -> list[dict[str, Any]]:
        """
        ポジション検索結果をキャッシュする

        Args:
            positions: 検索結果

        Returns:
            処理後のポジションリスト
        """
        if not positions:
            return []

        for position in positions:
            real_id = str(position["ID"])
            try:
                position["ID"] = self._encrypt_id(real_id)
            except Exception:
                self._logger.exception("ポジションID暗号化失敗 %s", real_id)
                return []

        return positions

    def save_position_detail(
        self,
        encrypted_position_id: str,
        position_detail: dict,
    ):
        """
        ポジション詳細APIレスポンスをキャッシュする

        Args:
            encrypted_position_id: 暗号化されたポジションID
            position_detail: ポジション詳細APIレスポンス
        """
        try:
            real_id = self.decrypt_id(encrypted_position_id)
            position_details = self._position_details.setdefault(get_session_id(), {})
            position_details[real_id] = position_detail
        except Exception:
            self._logger.exception("ポジションID復号化: %s", encrypted_position_id)

    def get_position_detail(self, encrypted_position_id: str) -> dict | None:
        """
        キャッシュからポジション詳細APIレスポンスを取得

        Args:
            encrypted_position_id: 暗号化されたポジションID

        Returns:
            ジション詳細APIレスポンス
        """
        try:
            real_id = self.decrypt_id(encrypted_position_id)
            position_details = self._position_details.setdefault(get_session_id(), {})
            return (
                copy.deepcopy(position_details.get(real_id))
                if real_id in position_details
                else None
            )
        except Exception:
            self._logger.exception(
                "ポジションID復号化が失敗しました: %s", encrypted_position_id
            )
            return None

    def save_company_detail(
        self,
        encrypted_position_id: str,
        company_detail: dict,
    ):
        """
        会社詳細APIレスポンスをキャッシュする

        Args:
            encrypted_position_id: 暗号化されたポジションID
            company_detail: 会社詳細APIレスポンス
        """
        try:
            real_id = self.decrypt_id(encrypted_position_id)
            company_details = self._company_details.setdefault(get_session_id(), {})
            company_details[real_id] = company_detail
        except Exception:
            self._logger.exception(
                "ポジションID復号化が失敗しました: %s", encrypted_position_id
            )

    def get_company_detail(
        self,
        encrypted_position_id: str,
    ) -> dict | None:
        """
        キャッシュから会社詳細APIレスポンスを取得

        Args:
            encrypted_position_id: 暗号化されたポジションID

        Returns:
            会社詳細APIレスポンス
        """
        try:
            real_id = self.decrypt_id(encrypted_position_id)
            company_details = self._company_details.setdefault(get_session_id(), {})
            return (
                copy.deepcopy(company_details.get(real_id))
                if real_id in company_details
                else None
            )
        except Exception:
            self._logger.exception(
                "ポジションID復号化が失敗しました: %s", encrypted_position_id
            )
            return None

    def save_business_detail(
        self,
        encrypted_position_id: str,
        business_detail: dict,
    ):
        """
        業界詳細APIレスポンスをキャッシュする

        Args:
            encrypted_position_id: 暗号化されたポジションID
            business_detail: 業界詳細APIレスポンス
        """
        try:
            real_id = self.decrypt_id(encrypted_position_id)
            business_details = self._business_details.setdefault(get_session_id(), {})
            business_details[real_id] = business_detail
        except Exception:
            self._logger.exception(
                "ポジションID復号化が失敗しました: %s", encrypted_position_id
            )

    def get_business_detail(
        self,
        encrypted_position_id: str,
    ) -> dict | None:
        """
        キャッシュから業界詳細APIレスポンスを取得

        Args:
            encrypted_position_id: 暗号化されたポジションID

        Returns:
            業界詳細APIレスポンス
        """
        try:
            real_id = self.decrypt_id(encrypted_position_id)
            business_details = self._business_details.setdefault(get_session_id(), {})
            return (
                copy.deepcopy(business_details.get(real_id))
                if real_id in business_details
                else None
            )
        except Exception:
            self._logger.exception("ポジションID: %s", encrypted_position_id)
            return None

    def save_position_recommendations(
        self,
        recommendation_themes: list[str],
    ) -> dict[str, str]:
        """
        おすすめのリンクマッピング作成

        Args:
            recommendation_themes: おすすめパスリスト

        Returns:
            レコメンドリンクマッピング
        """
        return {theme: self._encrypt_id(theme) for theme in recommendation_themes}

    def get_position_recommendation_theme(
        self,
        encrypted_theme: str,
    ) -> str | None:
        """
        おすすめのリンクマッピングからパスを取得

        Args:
            encrypted_theme: 暗号化されたおすすめテーマ

        Returns:
            おすすめテーマ
        """
        try:
            return self.decrypt_id(encrypted_theme)
        except Exception:
            return None

    def save_user_preference(
        self,
        preference: dict[str, Any],
    ):
        """
        ユーザーの検索条件をキャッシュする

        Args:
            preference: ユーザーの検索条件
        """
        self._logger.debug(f"save_user_preference: {preference}")
        session_data = self._user_preferences.setdefault(get_session_id(), {})
        session_data.update(preference)

    def get_user_preferences(
        self,
    ) -> dict | None:
        """
        キャッシュからユーザーの検索条件を取得

        Args:
            None

        Returns:
            ユーザーの検索条件
        """
        session_data = self._user_preferences.setdefault(get_session_id(), {})
        return copy.deepcopy(session_data) if session_data else {}
