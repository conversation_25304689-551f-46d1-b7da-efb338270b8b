from enum import StrEnum
from sqlalchemy import Column, DateTime, Foreign<PERSON>ey, Integer, String, ARRAY
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy.dialects.postgresql import JSONB
from database import Base
from pydantic import BaseModel, EmailStr, Field, model_validator, field_validator
import re
from datetime import datetime


class UserProfile(Base):
    __tablename__ = "user_profiles"

    id = Column(Integer, primary_key=True)
    session_id: Mapped[str] = mapped_column(ForeignKey("chat_sessions.session_id"))
    user_name = Column(String, nullable=True)
    user_purpose = Column(String, nullable=True)
    interest_tendency = Column(String, nullable=True)
    job_search_motivation = Column(String, nullable=True)
    current_job_experience_years = Column(Integer, nullable=True)
    current_job_description = Column(String, nullable=True)
    job_search_filter = Column(JSONB, nullable=True)
    job_feedback_positive = Column(ARRAY(String), nullable=True)
    job_feedback_negative = Column(ARRAY(String), nullable=True)
    miidas_registration_user_data: Mapped[JSONB] = mapped_column(JSONB, nullable=True)
    deleted_at = Column(DateTime, nullable=True)


class IDNameModel(BaseModel):
    id: int = Field(..., alias="ID")
    name: str = Field(..., alias="Name")


class AddressModel(BaseModel):
    prefecture: IDNameModel
    city: IDNameModel


class BaseJSONUserProfile(BaseModel):
    @classmethod
    def has_field_with_alias(cls, alias: str) -> bool:
        """
        aliasが存在するかどうかを返す
        """
        return any(field.alias == alias for field in cls.model_fields.values())

class JSONUserProfileBasicInfo(BaseJSONUserProfile):
    """
    ユーザーの基本的なプロフィールモデル
    DBに保存時はUserProfileのmiidas_registration_user_dataにJSONとして格納される
    """

    gender: int = Field(..., ge=1, le=2)  # 性別: 1=男性, 2=女性
    last_name: str = Field(..., max_length=50, alias="lastName")
    first_name: str = Field(..., max_length=50, alias="firstName")
    last_name_kana: str = Field(..., max_length=50, alias="lastNameKana")
    first_name_kana: str = Field(..., max_length=50, alias="firstNameKana")
    birth_year: int = Field(..., alias="birthYear")
    birth_month: int = Field(..., ge=1, le=12, alias="birthMonth")
    email: EmailStr
    # TODO: password保存前にハッシュに変換
    password: str = Field(..., min_length=8, max_length=16)
    phone_no: str = Field(..., min_length=10, max_length=13, alias="phoneNo")
    prefecture: IDNameModel
    city: IDNameModel

    @field_validator("password")
    @classmethod
    def validate_password(cls, v):
        """
        パスワードの妥当性検証
        英数字を含む8-16文字
        """
        if not re.match(r"^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,16}$", v):
            raise ValueError("パスワードは英字と数字を含む8-16文字である必要があります")
        return v

# Get the current year
CURRENT_DATETIME = datetime.now()
CURRENT_YEAR = CURRENT_DATETIME.year
MINIMUM_YEAR = 1940  # 卒業年、入社年または退社年


class JSONUserProfileEducation(BaseJSONUserProfile):
    """
    ユーザーの学歴モデル。
    DBに保存時はUserProfileのmiidas_registration_user_dataにJSONとして格納される
    """

    school_type: int = Field(
        ...,
        ge=1,
        le=7,
        alias="schoolType",
    )  # 学校種別: 1=大学院, 2=大学, 3=短期大学, 4=専門学校, 5=高等専門学校, 6=高等学校, 7=中学校
    graduation_year: int = Field(
        ...,
        ge=MINIMUM_YEAR,
        le=CURRENT_YEAR,
        alias="graduationYear",
    )  # 卒業年: 1940年から現在の年まで
    english_level: int = Field(
        ...,
        ge=1,
        le=4,
        alias="englishLevel",
    )  # 英語レベル: 1=あてはまるものはない, 2=日常会話レベル, 3=ビジネス会話レベル, 4=ネイティブレベル
    school_name: str = Field(
        ...,
        max_length=200,
        alias="schoolName",
    )
    department_type: IDNameModel = Field(
        ...,
        alias="departmentType",
    )
    professional_training_college_category: IDNameModel = Field(
        ...,
        alias="professionalTrainingCollegeCategory",
    )


class JSONUserProfileCarrer(BaseJSONUserProfile):
    """
    ユーザーの経歴モデル。
    DBに保存時はUserProfileのmiidas_registration_user_dataにJSONとして格納される
    """

    exp_company_num: int = Field(
        ...,
        ge=1,
        le=11,
        alias="expCompanyNum",
    )  # 経験社数 1：0社, 2：1社, 3：2社, 4：3社, 5：4社, 6：5社, 7：6社, 8：7社, 9：8社, 10：9社, 11：10社以上
    management_exp_term: int | None = Field(
        None,
        ge=1,
        le=12,
        alias="managementExpTerm",
    )  # 今までのマネジメント経験年数: 1: 経験なし, 2: 1年未満, 3: 1年以上, 4: 2年以上, 5: 3年以上, 6: 4年以上, 7: 5年以上, 8: 6年以上, 9: 7年以上, 10: 8年以上, 11: 9年以上, 12: 10年以上
    management_people_num: int | None = Field(
        None,
        ge=0,
        le=5,
        alias="managementPeopleNum",
    )  # 今までのマネジメント経験人数: 1=1〜4人, 2=5〜9人, 3=10〜29人, 4=30〜99人, 5=100人以上
    company_name: str | None = Field(
        None,
        max_length=255,
        alias="companyName",
    )  # 企業名
    industry_small: IDNameModel | None = Field(
        None,
        alias="industrySmall",
    )  # 業種ID
    employee_num: int | None = Field(
        None,
        ge=1,
        le=7,
        alias="employeeNum",
    )  # 企業規模1：10人未満, 2：10〜29人, 3：30～99人, 4：100〜299人, 5：300〜999人, 6：1000〜2999人, 7：3000人以上
    employment_type: int | None = Field(
        None,
        ge=1,
        le=6,
        alias="employmentType",
    )  # 直近企業の雇用形態1：正社員, 2：契約社員, 3：役員（任用契約）, 4：業務委託, 5：派遣社員, 6：アルバイト
    job_type_small: IDNameModel | None = Field(
        None,
        alias="jobTypeSmall",
    )  # 職種ID
    income: int | None = Field(
        None,
        ge=1,
        le=10000,
        alias="income",
    )  # 年収 最大1億
    join_year: int | None = Field(
        None,
        ge=MINIMUM_YEAR,
        le=CURRENT_YEAR,
        alias="joinYear",
    )
    join_month: int | None = Field(
        None,
        ge=1,
        le=12,
        alias="joinMonth",
    )
    retire_year: int | None = Field(
        None,
        ge=MINIMUM_YEAR,
        le=CURRENT_YEAR,
        alias="retireYear",
    )
    retire_month: int | None = Field(
        None,
        ge=1,
        le=12,
        alias="retireMonth",
    )

    @model_validator(mode="after")
    def conditional_validation(self):
        """
        条件付きバリデーション
        """
        # 経験者数が1社以上であればその他の項目は必須になる
        # マスタの1=ゼロ社
        # マスタの2=1社なので、ここでは1より大きいで比較する
        if self.exp_company_num > 1:
            if self.management_exp_term is None:
                raise ValueError("今までのマネジメント経験年数は必須です")
            elif self.management_exp_term > 1 and self.management_people_num is None:
                raise ValueError("今までのネジメント経験人数は必須です")
            if self.company_name is None:
                raise ValueError("企業名は必須です")
            if self.income is None:
                raise ValueError("年収は必須です")
            if self.industry_small.id is None:
                raise ValueError("業種は必須です")
            elif self.industry_small.id < 101010 or self.industry_small.id > 441010:
                raise ValueError("業種は無効です")
            if self.employee_num is None:
                raise ValueError("企業規模は必須です")
            if self.job_type_small.id is None:
                raise ValueError("職種は必須です")
            elif self.job_type_small.id < 101010 or self.job_type_small.id > 451126:
                raise ValueError("職種は無効です")
            if self.join_year is None or self.join_month is None:
                raise ValueError("入社年月は必須です")
            if self.employment_type is None:
                raise ValueError("雇用形態は必須です")

            # 退社年月はNoneであれば在籍中という意味
            if self.retire_year is not None and self.retire_month is not None:
                # 但し、退社年月は入社年月よりも未来でなければなりません
                join_date = datetime(self.join_year, self.join_month, 1)
                retire_date = datetime(self.retire_year, self.retire_month, 1)

                if retire_date <= join_date:
                    raise ValueError("退社年月は入社年月よりも過去であってはなりません")
        return self

    @field_validator("retire_year", "retire_month", mode="before")
    @classmethod
    def empty_str_to_none(cls, v):
        if v == "":
            return None
        return v


class JSONUserProfileWill(BaseJSONUserProfile):
    """
    ユーザーの希望条件モデル。
    DBに保存時はUserProfileのmiidas_registration_user_dataにJSONとして格納される
    """

    will_income: int = Field(
        ...,
        ge=100,
        le=2000,
        alias="willIncome",
    )  # 希望年収: 100万円から2000万円まで,
    will_work_addresses_cities: list[AddressModel] = Field(
        ...,
        min_length=1,
        max_length=20,
        alias="willWorkAddressesCities",
    )  # 希望勤務地IDリスト
    will_job_types_smalls: list[IDNameModel] = Field(
        ...,
        min_length=1,
        alias="willJobTypesSmalls",
    )  # 希望職種IDリスト
    will_job_change_period: int = Field(
        ...,
        alias="willJobChangePeriod",
    )  # 転職希望時期: 1=1ヶ月以内, 2=3ヶ月以内, 3=6ヶ月以内, 4=1年以内, 5=1年よりも先, 6=転職を考えていない
    is_rpo_agreement: bool = Field(
        ...,
        alias="isRpoAgreement",
    )  # 希望する求人マッチングサービスを利用するかどうか


class JSONSearchKeyword(BaseModel):
    """
    キーワード検索リクエストモデル
    """

    keyword: str = Field(..., min_length=1)

    @model_validator(mode="after")
    def validate_keyword(self):
        """
        キーワードの妥当性検証
        """
        if not self.keyword or self.keyword.strip() == "":
            raise ValueError("キーワードは必須です")

        self.keyword = self.keyword.strip()

        return self


class LocationTypeEnum(StrEnum):
    residence = "居住地"
    work_location = "希望勤務地"


class JSONSearchLocation(BaseModel):
    """
    通勤エリア検索リクエストモデル
    """

    location_type: LocationTypeEnum | None = Field(None)
    prefecture_name: str | None = Field(None)
    city_name: str | None = Field(None)

    @model_validator(mode="after")
    def validate_keyword(self):
        if not self.city_name or self.city_name.strip() == "":
            raise ValueError("市区町村名は必須です")

        self.prefecture_name = (
            self.prefecture_name.strip() if self.prefecture_name else None
        )
        self.city_name = self.city_name.strip() if self.city_name else None

        return self
