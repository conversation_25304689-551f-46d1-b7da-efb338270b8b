from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .tool_definition import ToolDefinition
from database import Base


class AgentTool(Base):
    __tablename__ = "agent_tools"

    id = Column(Integer, primary_key=True, autoincrement=False)
    agent_id: Mapped[int] = mapped_column(ForeignKey("agents.id"))
    tool_definition_id: Mapped[int] = mapped_column(ForeignKey("tool_definitions.id"), nullable=False)
    return_direct = Column(Boolean)
    tool: Mapped[ToolDefinition] = relationship()

    @property
    def tool_name(self) -> str | None:
        if self.tool is None:
            return None
        return self.tool.name
