from sqlalchemy import Column, <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.orm import Mapped, relationship, mapped_column
from database import Base


class Workflow(Base):
    __tablename__ = "workflows"

    id = Column(Integer, primary_key=True, autoincrement=False)
    src_agent_id: Mapped[int] = mapped_column(ForeignKey("agents.id"))
    dest_agent_id: Mapped[int] = mapped_column(ForeignKey("agents.id"))
    description = Column(String)

    # 循環参照になるので、"Agent"を文字列で定義する
    dest_agent: Mapped["Agent"] = relationship(
        primaryjoin="Workflow.dest_agent_id == Agent.id"
    )
