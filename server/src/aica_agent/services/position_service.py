from repositories.api_repo import AICAAPIRepository
from repositories.position_repo import PositionRepository
from services.base_service import BaseService


class PositionService(BaseService):
    """
    ポジション関連操作(APIリクエストやキャッシュ保存/取得)サービス
    """

    def __init__(
        self,
        position_repository: PositionRepository,
        aica_api_repository: AICAAPIRepository,
    ) -> None:
        super().__init__()

        self._position_repository = position_repository
        self._aica_api_repository = aica_api_repository

    async def get_position_detail(
        self,
        encrypted_position_id: str,
    ) -> dict | None:
        """
        ポジション詳細APIを呼び出して、ポジション詳細を取得してキャッシュに保存する

        Args:
            encrypted_position_id: 変換後ポジションID

        Returns:
            ポジション詳細
        """
        try:
            position_id = self._position_repository.decrypt_id(encrypted_position_id)
        except Exception as e:
            self.logger.exception(e)
            return None

        api_path = f"positions/detail/{position_id}"
        self.logger.debug(f"ポジション詳細APIリクエスト: {api_path}")
        _, position_detail = await self._aica_api_repository.post(api_path)

        if position_detail:
            self._position_repository.save_position_detail(
                encrypted_position_id,
                position_detail,
            )
        return position_detail

    async def get_company_detail(
        self,
        encrypted_position_id: str,
    ) -> dict | None:
        """
        会社詳細APIを呼び出して、会社詳細を取得してキャッシュに保存する

        Args:
            encrypted_position_id: 変換後ポジションID

        Returns:
            会社詳細
        """
        try:
            position_id = self._position_repository.decrypt_id(encrypted_position_id)
        except Exception as e:
            self.logger.exception(e)
            return None

        api_path = f"companies/detail/position_id/{position_id}"
        self.logger.debug(f"会社詳細APIリクエスト: {api_path}")
        _, company_detail = await self._aica_api_repository.get(api_path)

        if company_detail:
            self._position_repository.save_company_detail(
                encrypted_position_id,
                company_detail,
            )
        return company_detail

    async def get_business_detail(
        self,
        encrypted_position_id: str,
    ) -> dict | None:
        """
        業界詳細APIを呼び出して、業界詳細を取得してキャッシュに保存する

        Args:
            encrypted_position_id: 変換後ポジションID

        Returns:
            業界詳細
        """
        try:
            position_id = self._position_repository.decrypt_id(encrypted_position_id)
        except Exception as e:
            self.logger.exception(e)
            return None

        api_path = f"businesses/detail/position_id/{position_id}"
        self.logger.debug(f"業界詳細APIリクエスト: {api_path}")
        _, business_detail = await self._aica_api_repository.get(api_path)

        if business_detail:
            self._position_repository.save_business_detail(
                encrypted_position_id,
                business_detail,
            )
        return business_detail

    async def get_position_recommendation(
        self,
        encrypted_theme: str,
    ) -> list[dict] | None:
        """
        APIサーバーからおすすめポジションを取得する

        Args:
            encrypted_theme: 暗号化されたおすすめテーマ
            search_conditions: ユーザーの検索条件

        Returns:
            おすすめのポジションリスト
        """
        theme = self._position_repository.get_position_recommendation_theme(
            encrypted_theme
        )
        if not theme:
            return None

        search_conditions = self._position_repository.get_user_preferences()

        api_path = f"positions/recommendations/{theme}"
        self.logger.debug(f"おすすめポジションAPIリクエスト: {api_path}")
        _, response_data = await self._aica_api_repository.post(
            api_path,
            json=search_conditions,
        )

        if not response_data or "Positions" not in response_data:
            return None
        
        # 分析用のログ出力
        all_positions = response_data.get("AllPositionIds", [])
        self.logger.info(
            "analyze_position_recommendations",
            extra={"theme": theme, "count": len(all_positions) if all_positions else 0},
        )

        return self._position_repository.process_and_cache_positions(
            response_data["Positions"]
        )

    async def load_more(
        self,
        search_key: str,
        offset: int,
        limit: int,
    ):
        position_ids = self._position_repository.get_search_result_position_ids(
            search_key,
            offset,
            limit,
        )

        api_path = "positions/summaries"
        self.logger.debug(f"ポジションもっとみるAPIリクエスト: {api_path}")
        _, response_data = await self._aica_api_repository.post(
            api_path,
            json={"PositionIDs": position_ids},
        )

        if not response_data or "Positions" not in response_data:
            count = self._position_repository.remove_search_result_positions_ids(
                search_key,
                position_ids,
            )
            return (count, [])
        else:
            positions = response_data["Positions"]
            returned_ids = {p["ID"] for p in positions}
            # サマリが取れなかったポジションID
            non_avaible_position_ids = [
                pid for pid in position_ids if pid not in returned_ids
            ]
            positions = self._position_repository.process_and_cache_positions(
                positions,
            )

            return (
                (
                    # サマリが取れなかったポジションIDがある場合、メモリから消してから有効なポジションID数を返す
                    self._position_repository.remove_search_result_positions_ids(
                        search_key,
                        non_avaible_position_ids,
                    )
                    if non_avaible_position_ids
                    else self._position_repository.get_search_result_count(
                        search_key,
                    )
                ),
                positions,
            )
