logging:
  version: 1
  disable_existing_loggers: False
  formatters:
    json:
      (): "pythonjsonlogger.jsonlogger.JsonFormatter"
      format: "%(asctime)s %(levelname)s %(name)s %(session_id)s %(request_id)s %(caller)s %(message)s"
      json_ensure_ascii: False
  handlers:
    console:
      class: "logging.StreamHandler"
      formatter: "json"
      stream: "ext://sys.stdout"
    file:
      class: "logging.FileHandler"
      level: "DEBUG"
      formatter: "json"
      filename: "/tmp/aica_agent.log"
      mode: "a"
  root:
    level: "DEBUG"
    handlers:
      - console
      - file
  loggers:
    gunicorn:
      level: INFO
      handlers:
        - console
      propagate: False
    uvicorn:
      level: "INFO"
      handlers:
        - console
      propagate: False
    LiteLLM:
      level: "WARNING"
      propagate: False
      handlers:
        - console
    sqlalchemy:
      level: "WARNING"
      propagate: False
      handlers:
        - console
    aica_agent:
      level: "DEBUG"
      propagate: True
db:
  url: "postgresql+psycopg://${AICA_AGENT_DB_USER}:${AICA_AGENT_DB_PASSWORD}@${AICA_AGENT_DB_HOST}:${AICA_AGENT_DB_PORT}/${AICA_AGENT_DB_NAME}?sslmode=${AICA_AGENT_DB_SSLMODE}"
model_list:
  - model: openai/gpt-4.1
    model_settings:
      temperature: 1.0
  - model: openai/gpt-5
    model_settings:
      temperature: 1.0
      reasoning:
        effort: low
mcp:
  url: "${AICA_AGENT_MCP_ENDPOINT}"
api:
  aica: "${AICA_AGENT_API_ENDPOINT}"
  miidas: "${AICA_AGENT_MIIDAS_API_ENDPOINT}"
  # 30 second timeout
  timeout: 30
