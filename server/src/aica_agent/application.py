"""
FastAPI入口
"""
from contextlib import asynccontextmanager
from logging import getLogger
from fastapi import <PERSON><PERSON><PERSON>, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from containers import Container
import endpoints
from utils.logging import add_tracing_info, clear_tracing_info
from utils.const import LOGGER_PREFIX

container = Container()


@asynccontextmanager
async def lifespan(_: FastAPI):
    await container.init_resources()
    yield
    await container.shutdown_resources()


app = FastAPI(lifespan=lifespan)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)


@app.middleware("http")
async def add_tracing_info_context(request: Request, call_next):
    """
    リクエスト毎にセッションIDとリクエストIDをcontextに追加する

    Args:
        request: リクエスト
        call_next: 次の処理

    Returns:
        レスポンス
    """
    add_tracing_info(request)
    try:
        response = await call_next(request)
        return response
    finally:
        clear_tracing_info()


app.container = container
app.include_router(endpoints.router)

@app.exception_handler(RequestValidationError)
async def validation_error_handler(request: Request, exc: RequestValidationError):
    """
    リクエストバリデーションエラー処理
    現在リクエストパラメータバリデーションはプロフィール入力しかないですが、
    フロントの方は制御しているので、サーバー側のバリデーションエラーが発生しないはず
    発生する場合、Attackかいたずらとしか考えられないので、詳細エラー内容を返さない。
    フロント側もサーバー側バリデーションエラーの発生を考慮してない。

    Args:
        request: リクエスト
        exc: 例外

    Returns:
        JSONResponse: エラーレスポンス
    """
    logger = getLogger(f"{LOGGER_PREFIX}.{__name__}")
    logger.error(f"ValidationError caught: {exc} for URL: {request.url}")

    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content={},
    )
