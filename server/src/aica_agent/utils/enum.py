from enum import IntEnum, StrEnum


class PageName(StrEnum):
    CHAT = "Chat"
    POSITION_DETAIL = "PositionDetail"
    PROFILE_BASIC_INFO = "BasicInfo"
    PROFILE_CARRER = "Carrer"
    PROFILE_EDUCATION = "Education"
    PROFILE_WILL = "Will"


class HttpMethod(StrEnum):
    GET = "GET"
    POST = "POST"


class ComponentName(StrEnum):
    POSITION = "position"
    RECOMMENDATION = "recommendation"


class ProfileInputRequestPath(StrEnum):
    BASIC_INFO = "profile/basic"
    CARRER = "profile/carrer"
    EDUCATION = "profile/education"
    WILL = "profile/will"

class ApplyResult(IntEnum):
    UNKNOWN = -999
    INVALID_SESSION_STATUS = -10
    REGISTER_ALREADY = 10
    REGISTER_SUCCESS = 20
    REGISTER_FAIL = 30
    MEETING_APPLICATION_ALREADY = 40
    MEETING_APPLICATION_SUCCESS = 50
    MEETING_APPLICATION_FAIL = 60