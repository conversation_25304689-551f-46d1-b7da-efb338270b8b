"""
ログに、セッションIDとリクエストIDを出するため準備
"""

import logging
from typing import Optional
from contextvars import ContextVar
from fastapi import Request

session_id_var: ContextVar[Optional[str]] = ContextVar("Session ID", default=None)
request_id_var: ContextVar[Optional[str]] = ContextVar("Request ID", default=None)

old_factory = logging.getLogRecordFactory()


def record_factory(*args, **kwargs):
    record = old_factory(*args, **kwargs)
    record.session_id = session_id_var.get()
    record.request_id = request_id_var.get()
    record.caller = record.pathname + ":" + str(record.lineno)
    return record


def set_session_id(session_id: str) -> None:
    session_id_var.set(session_id)


def get_session_id() -> str | None:
    return session_id_var.get()


def clear_session_id() -> None:
    session_id_var.set(None)


def set_request_id(session_id: str) -> None:
    request_id_var.set(session_id)


def get_request_id() -> str | None:
    return request_id_var.get()


def clear_request_id() -> None:
    request_id_var.set(None)


def add_tracing_info(request: Request):
    session_id = request.headers.get("X-SESSION-ID")
    set_session_id(session_id)

    request_id = request.headers.get("X-REQUEST-ID")
    set_request_id(request_id)


def clear_tracing_info():
    clear_session_id()
    clear_request_id()
