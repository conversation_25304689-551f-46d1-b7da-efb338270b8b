"""
WebsocketレスポンスStructure
"""

from enum import StrEnum
import json
import uuid
from pydantic import BaseModel

from domain.entities.chat_session import ChatSessionStatus
from utils.logging import get_session_id


class MessageType(StrEnum):
    MESSAGE = "message"
    POSITION_SEARCH_RESULT = "position_search_result"
    ERROR = "error"
    END = "end"


class ChatStreamResponseModel(BaseModel):
    session_id: str
    session_status: int
    message_id: str
    message_type: MessageType
    position_id: str | None = None
    message: str


class ChatStreamResponse:
    def __init__(
        self,
        position_id: str = None,
        message_id: str = None,
    ):
        self._model = ChatStreamResponseModel(
            session_id=get_session_id(),
            session_status=ChatSessionStatus.CHATTING,
            position_id=position_id,
            message_id=message_id if message_id else str(uuid.uuid4()),
            message_type=MessageType.MESSAGE,
            message="",
        )

    def create_end_response(
        self, session_status: int = ChatSessionStatus.CHATTING
    ) -> ChatStreamResponseModel:
        """
        １回のインプットに対してのレスポン終了

        Args:
            session_status: チャットステータス

        Returns:
            LLM回答終了レスポンス
        """
        return self.create_response(MessageType.END, "", session_status)

    def create_tool_result_response(
        self,
        result: dict,
    ) -> ChatStreamResponseModel:
        """
        return_direct=Trueのツールの場合、実行結果をそのままレスポンスする

        Args:
            result: ツール実行結果

        Returns:
            LLM回答: ツール実行結果
        """
        return self.create_response(
            MessageType.POSITION_SEARCH_RESULT,
            json.dumps(result),
        )

    def create_message_response(
        self,
        message: str,
    ) -> ChatStreamResponseModel:
        """
        LLM回答をレスポンスする

        Args:
            message: LLM回答

        Returns:
            LLM回答: メッセージ
        """
        return self.create_response(
            MessageType.MESSAGE,
            message,
        )

    def create_error_response(
        self,
        error: str,
    ) -> ChatStreamResponseModel:
        """
        エラー発生した場合のレスポンス

        Args:
            error: エラーメッセージ

        Returns:
            LLM回答: エラーメッセージ
        """
        return self.create_response(
            MessageType.ERROR,
            error,
        )

    def create_response(
        self,
        message_type: MessageType,
        message: str,
        session_status: int = ChatSessionStatus.CHATTING,
    ) -> ChatStreamResponseModel:
        """
        クライアントへの返信

        Args:
            message_type: 返信タイプ
            message: 返信内容
            session_status: チャットステータス

        Returns:
            クライアントへの返信
        """
        self._model.session_status = session_status
        self._model.message_type = message_type
        self._model.message = message
        return self._model
