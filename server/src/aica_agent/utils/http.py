import asyncio
from aiohttp import ClientSession
from fastapi import status
from logging import getLogger

from utils.const import LOGGER_PREFIX
from utils.logging import get_request_id, get_session_id

_logger = getLogger(f"{LOGGER_PREFIX}.{__name__}")


async def request(
    client: ClientSession,
    method: str,
    path: str,
    **kwargs,
) -> tuple[int | None, dict | list | None]:
    """
    指定されたAPIパスに対して指定されたメソッドでHTTP リクエストを実行します。
    Args:
        path (str): リクエストを送信する対象パス。
        method (str): GET or POST
        **kwargs: requestメソッドに渡す追加引数。aiohttp.ClientSession.requestのkwargs参照
    Returns:
        HTTP Status
        Response Body
    """
    headers = {
        "X-SESSION-ID": get_session_id(),
        "X-REQUEST-ID": get_request_id(),
    }
    kwargs.setdefault("headers", {}).update(headers)
    _logger.debug(f"AICA APIリクエスト: {method} {path} {kwargs}")

    try:
        async with client.request(method, path, **kwargs) as response:
            if response.status == status.HTTP_200_OK:
                if response.content_length == 0:
                    _logger.warning(
                        f"API request to {path} returned empty body: {response.status}"
                    )
                    return response.status, None

                body = await response.text()
                if not body.strip():
                    _logger.warning(
                        f"API request to {path} returned empty body: {response.status}"
                    )
                    return response.status, None

            return response.status, await response.json()
    except asyncio.TimeoutError:
        _logger.exception(f"Timeout calling AICA API: {path}")
        return None, None
    except Exception as e:
        _logger.exception(f"Error calling AICA API {path}: {e}")
        return None, None
