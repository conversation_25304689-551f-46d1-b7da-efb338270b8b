from utils.crypt import create_secret_key
from utils.logging import get_session_id
from utils import sync_dict

class SessionKeyManager:
    """
    セッションIDごとにキーを保持するクラス
    """

    def __init__(self):
        self._session_keys = sync_dict.SynchronizedDict[str, bytes]()

    def get_or_create_session_key(self) -> bytes:
        """
        暗号化・復号化キー取得／生成

        Args:
            None

        Returns:
            暗号化・復号化キー
        """
        session_id = get_session_id()
        if session_id not in self._session_keys:
            self._session_keys[session_id] = create_secret_key()
        return self._session_keys[session_id]

    def set_session_key(self, key):
        self._session_keys[get_session_id()] = key
