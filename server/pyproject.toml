[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "AICA-Agent"
version = "0.0.1"
description = "エージェントサーバー"
readme = "README.md"
requires-python = ">=3.12,<3.13"
classifiers = [
    "Programming Language :: Python :: 3.12",
    "Operating System :: OS Independent",
]
dependencies = [
    "aiohttp==3.12.15",
    "backoff==2.2.1",
    "boto3==1.39.6",
    "dependency-injector==4.46.0",
    "fastapi==0.116.0",
    "gunicorn==23.0.0",
    "mcp==1.16.0",
    "openai-agents==0.3.3",
    "psycopg==3.2.6",
    "psycopg-binary==3.2.6",
    "PyYAML==6.0.2",
    "SQLAlchemy==2.0.40",
    "uvicorn==0.34.0",
    "aiohappyeyeballs==2.6.1",
    "aiosignal==1.4.0",
    "annotated-types==0.7.0",
    "anyio==4.9.0",
    "attrs==25.3.0",
    "botocore==1.39.11",
    "certifi==2025.7.14",
    "cffi==1.17.1",
    "charset-normalizer==3.4.2",
    "click==8.2.1",
    "colorama==0.4.6",
    "cryptography==45.0.5",
    "distro==1.9.0",
    "dnspython==2.7.0",
    "email_validator==2.2.0",
    "fastapi-cli==0.0.8",
    "fastapi-cloud-cli==0.1.4",
    "filelock==3.18.0",
    "frozenlist==1.7.0",
    "fsspec==2025.7.0",
    "griffe==1.8.0",
    "h11==0.16.0",
    "hf-xet==1.1.5",
    "httpcore==1.0.9",
    "httptools==0.6.4",
    "httpx==0.28.1",
    "httpx-sse==0.4.1",
    "huggingface-hub==0.33.4",
    "idna==3.10",
    "importlib_metadata==8.7.0",
    "Jinja2==3.1.6",
    "jiter==0.10.0",
    "jmespath==1.0.1",
    "jsonschema==4.25.0",
    "jsonschema-specifications==2025.4.1",
    "markdown-it-py==3.0.0",
    "MarkupSafe==3.0.2",
    "mdurl==0.1.2",
    "multidict==6.6.3",
    "openai==v1.109.1",
    "packaging==25.0",
    "propcache==0.3.2",
    "pycparser==2.22",
    "pydantic==2.11.7",
    "pydantic-settings==2.10.1",
    "pydantic_core==2.33.2",
    "Pygments==2.19.2",
    "python-dateutil==2.9.0.post0",
    "python-dotenv==1.1.1",
    "python-multipart==0.0.20",
    "referencing==0.36.2",
    "regex==2024.11.6",
    "requests==2.32.4",
    "rich==14.0.0",
    "rich-toolkit==0.14.8",
    "rignore==0.6.4",
    "rpds-py==0.26.0",
    "s3transfer==0.13.1",
    "sentry-sdk==2.33.2",
    "shellingham==1.5.4",
    "six==1.17.0",
    "sniffio==1.3.1",
    "sse-starlette==2.4.1",
    "starlette==0.46.2",
    "tiktoken==0.9.0",
    "tokenizers==0.21.2",
    "tqdm==4.67.1",
    "typer==0.16.0",
    "types-requests==2.32.4.20250611",
    "typing-inspection==0.4.1",
    "typing_extensions==4.14.1",
    "urllib3==2.5.0",
    "uvloop==0.21.0",
    "watchfiles==1.1.0",
    "websockets==15.0.1",
    "yarl==1.20.1",
    "zipp==3.23.0",
    "python-json-logger==3.0.0",
]

[project.optional-dependencies]
test = [
    "pytest>=8.0",
    "pytest-asyncio>=0.23.0",
    "pytest-cov>=5.0",
]

[project.urls]
Repository = "https://github.com/MIIDAS-Company/miidas_aica_agent"
Issues = "https://github.com/MIIDAS-Company/miidas_aica_agent/issues"

[tool.hatch.build.targets.wheel]
packages = ["aica_agent"]

[tool.hatch.metadata]
allow-direct-references = true
