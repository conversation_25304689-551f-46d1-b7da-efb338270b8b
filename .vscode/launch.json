{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "[Agent]Remote Debug",
      "type": "debugpy", 
      "request": "attach",
      "connect": {
        "host": "localhost",
        "port": 5678
      },
      "pathMappings": [
        {
          "localRoot": "${workspaceFolder}/server/src/aica_agent",
          "remoteRoot": "/home/<USER>/web"
        }
      ],
      "preLaunchTask": "start-debug-server",
      "justMyCode": false
    },
    {
      "name": "[Agent]Local Debug",
      "type": "debugpy",
      "request": "launch",
      "module": "uvicorn",
      "cwd": "${workspaceFolder}/server/src/aica_agent",
      "args": [
        "application:app",
        "--reload",
        "--host",
        "0.0.0.0",
        "--port",
        "8000",
        "--limit-concurrency",
        "100",
        "--timeout-keep-alive",
        "30"
      ],
      "envFile": "${workspaceFolder}/server/.env.local"
    },
    {
      "name": "[E2E]Local Debug",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/e2e/src/aica_client/main.py",
      "cwd": "${workspaceFolder}/e2e",
      "envFile": "${workspaceFolder}/e2e/.env.local"
    },
    {
      "name": "[Batch]Local Debug",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/cli/src/aica_batch/main.py",
      "args": ["${input:command}"],
      "cwd": "${workspaceFolder}/cli/src/aica_batch",
      "envFile": "${workspaceFolder}/cli/.env.local"
    }
  ],
  "inputs": [
    {
      "id": "command",
      "type": "pickString",
      "description": "Select the command of the batch to debug",
      "options": [
        {
          "label": "セッションクリーニング",
          "value": "clean_session"
        }
      ]
    }
  ]
}
